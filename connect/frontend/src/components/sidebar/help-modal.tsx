import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
  DialogHeader,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { HelpCircle, Sparkles, Zap, Brain, DollarSign, Copy, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';

export function HelpModal() {
  const [open, setOpen] = useState(false);
  const [promptInput, setPromptInput] = useState('');
  const [enhancedPrompt, setEnhancedPrompt] = useState('');
  const [isEnhancing, setIsEnhancing] = useState(false);

  const modelGuide = [
    {
      name: 'Gemini 2.5 Flash',
      icon: <Zap className="h-4 w-4" />,
      cost: 'Économique',
      costColor: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      bestFor: ['Rédaction rapide', 'Analyse de texte', 'Traduction', 'Résumés', 'Questions-réponses'],
      description: 'Modèle rapide et économique, parfait pour la plupart des tâches courantes.',
      recommended: true
    },
    {
      name: 'ChatGPT (GPT-4o)',
      icon: <Brain className="h-4 w-4" />,
      cost: 'Modéré',
      costColor: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      bestFor: ['Programmation', 'Analyse complexe', 'Créativité', 'Raisonnement logique'],
      description: 'Excellent équilibre entre performance et coût pour les tâches complexes.'
    },
    {
      name: 'Claude 4 Sonnet',
      icon: <Sparkles className="h-4 w-4" />,
      cost: 'Premium',
      costColor: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      bestFor: ['Analyse approfondie', 'Écriture créative', 'Recherche', 'Tâches sensibles'],
      description: 'Le plus avancé pour les tâches nécessitant une réflexion approfondie.'
    }
  ];

  const promptTips = [
    {
      title: 'Soyez spécifique',
      description: 'Plus votre demande est précise, meilleure sera la réponse.',
      example: '❌ "Écris un email" → ✅ "Écris un email de relance commercial pour un prospect qui n\'a pas répondu depuis 2 semaines"'
    },
    {
      title: 'Donnez du contexte',
      description: 'Expliquez le contexte et l\'objectif de votre demande.',
      example: '✅ "Je suis consultant en marketing digital et je dois présenter une stratégie social media à un client dans le secteur de la restauration"'
    },
    {
      title: 'Définissez le format',
      description: 'Précisez le format de sortie souhaité.',
      example: '✅ "Présente les résultats sous forme de liste à puces avec 3 points principaux"'
    },
    {
      title: 'Utilisez des exemples',
      description: 'Donnez des exemples pour clarifier vos attentes.',
      example: '✅ "Écris dans le même style que cet exemple : [insérer exemple]"'
    }
  ];

  const enhancePrompt = async () => {
    if (!promptInput.trim()) {
      toast.error('Veuillez saisir un prompt à améliorer');
      return;
    }

    setIsEnhancing(true);
    
    // Simulation d'amélioration du prompt (vous pouvez intégrer une vraie API ici)
    setTimeout(() => {
      const enhanced = `**Contexte et objectif :**
${promptInput}

**Instructions détaillées :**
- Adoptez un ton professionnel et bienveillant
- Structurez votre réponse de manière claire et logique
- Fournissez des exemples concrets quand c'est pertinent
- Vérifiez la cohérence et la précision de vos informations

**Format de sortie souhaité :**
Présentez votre réponse de manière organisée avec des titres et sous-titres si nécessaire.

**Contraintes spécifiques :**
- Réponse en français
- Longueur adaptée au sujet (ni trop court, ni trop long)
- Évitez le jargon technique sauf si nécessaire`;

      setEnhancedPrompt(enhanced);
      setIsEnhancing(false);
      toast.success('Prompt amélioré avec succès !');
    }, 2000);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copié dans le presse-papiers !');
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full text-xs bg-blue-50/50 hover:bg-blue-100/50 dark:bg-blue-950/30 dark:hover:bg-blue-900/40 border-blue-200/50 dark:border-blue-800/50 text-blue-700 dark:text-blue-300 backdrop-blur-sm transition-all duration-200"
        >
          <HelpCircle className="mr-1.5 h-3.5 w-3.5" />
          Guide & Astuces
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5 text-blue-600" />
            Guide d'utilisation d'Orchestra Connect
          </DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="models" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="models">Choisir son modèle</TabsTrigger>
            <TabsTrigger value="prompts">Améliorer ses prompts</TabsTrigger>
          </TabsList>
          
          <TabsContent value="models" className="space-y-4 max-h-[60vh] overflow-y-auto">
            <div className="grid gap-4">
              <div className="text-sm text-muted-foreground">
                Chaque modèle IA a ses forces. Voici comment choisir le bon modèle selon votre besoin :
              </div>
              
              {modelGuide.map((model, index) => (
                <Card key={index} className={`relative ${model.recommended ? 'ring-2 ring-green-500/20' : ''}`}>
                  {model.recommended && (
                    <Badge className="absolute -top-2 -right-2 bg-green-600 text-white">
                      Recommandé
                    </Badge>
                  )}
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2 text-base">
                        {model.icon}
                        {model.name}
                      </CardTitle>
                      <Badge className={model.costColor}>
                        <DollarSign className="h-3 w-3 mr-1" />
                        {model.cost}
                      </Badge>
                    </div>
                    <CardDescription>{model.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div>
                      <h4 className="font-medium text-sm mb-2">Idéal pour :</h4>
                      <div className="flex flex-wrap gap-1">
                        {model.bestFor.map((use, i) => (
                          <Badge key={i} variant="secondary" className="text-xs">
                            {use}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="prompts" className="space-y-4 max-h-[60vh] overflow-y-auto">
            <div className="grid gap-6">
              {/* Tips section */}
              <div className="space-y-4">
                <h3 className="font-semibold text-lg">Conseils pour de meilleurs prompts</h3>
                <div className="grid gap-3">
                  {promptTips.map((tip, index) => (
                    <Card key={index}>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">{tip.title}</CardTitle>
                        <CardDescription className="text-xs">{tip.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="text-xs font-mono bg-muted p-2 rounded text-muted-foreground">
                          {tip.example}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Prompt enhancer */}
              <div className="space-y-4">
                <h3 className="font-semibold text-lg">Améliorateur de prompts</h3>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Transformez votre prompt simple en prompt optimisé</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Votre prompt actuel :</label>
                      <Textarea
                        placeholder="Ex: Écris-moi un email pour mon client..."
                        value={promptInput}
                        onChange={(e) => setPromptInput(e.target.value)}
                        className="mt-1"
                        rows={3}
                      />
                    </div>
                    
                    <Button 
                      onClick={enhancePrompt} 
                      disabled={isEnhancing}
                      className="w-full"
                    >
                      {isEnhancing ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Amélioration en cours...
                        </>
                      ) : (
                        <>
                          <Sparkles className="mr-2 h-4 w-4" />
                          Améliorer le prompt
                        </>
                      )}
                    </Button>
                    
                    {enhancedPrompt && (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <label className="text-sm font-medium">Prompt amélioré :</label>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(enhancedPrompt)}
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            Copier
                          </Button>
                        </div>
                        <div className="bg-muted p-3 rounded text-sm whitespace-pre-wrap">
                          {enhancedPrompt}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
